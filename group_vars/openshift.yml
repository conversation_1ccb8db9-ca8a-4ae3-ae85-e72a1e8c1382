---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        Galaxy Standard GROUP Variables                                   #
# Version:                                                                        #
#               2023-08-23 WRC. Initial                                           #
# Create Date:  2023-08-23                                                        #
# Author:       <PERSON><PERSON><PERSON>, <PERSON> (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is where the group based dictionaries will be placed.        #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
lmco_openshift_products:
  # Install OpenShift
  - { name: galaxy_platform, target: aws,              provider: okd,  profile: default, state: present }
  # Base OpenShift configurations (certs, banners, ntp, etc.)
  - { name: galaxy_platform, target: redhat_openshift, provider: base, profile: default, state: present }

lmco_ocp:
  ca_cert: "{{ lookup('env', 'PWD') }}/files/ca.crt"
  cluster_admins:
    - c1620
    - deckerd2
    - dheidger
    - e314519
    - e358841
    - e384698
    - e401115
    - e407109
    - e415391
    - e419880
    - e420352
    - e421450
    - e422872
    - e437104
    - e437555
    - e451229
    - hodgswr
    - ldsams
    - n2808e
    - n6749f
    - n8573e
    - rbhatt
    - scottsn
  cluster_cert: "{{ lookup('env', 'PWD') }}/files/andromeda.crt"
  cluster_key: "{{ lookup('env', 'PWD') }}/files/andromeda.key"
  force_redeployment: false
  openshift_cluster: andromeda
  project_domain: ssc.lmco.com
  sso_client_id: space-spaceit-ocp-el8000
  sso_issuer: https://authuat.p.external.lmco.com
  sso_name: LMCO-SSO
  sso_secret: !vault |
    $ANSIBLE_VAULT;1.2;AES256;galaxy
    61643262373364636363366564363736646630613232666231666136323938666636393263303661
    3265376333663431666562633634306461613831623562360a623934316331383562383630353632
    66383764303139333336613262393063316236353230303962303331326338346230336634323538
    6462663263663666630a373838303736343864356433656137383131376533643837633638323436
    66383063303032646532663161623430356435353639396633663638346233653863643439393432
    6535383435356463366566656564326162376130356337366162
  sso_type: OpenID
  openshift_public_ip: ***************
  baremetal_network_cidr: **********/27
  aws:
    vpc_id: vpc-01d4a367eeec07620
    az: us-gov-west-1a
    region: us-gov-west-1
    aws_access_key_id: AKIAU4P4HTPI2ZNNFNNS
    aws_secret_access_key: !vault |
      $ANSIBLE_VAULT;1.2;AES256;galaxy
      66393336303233663365303364323731626530643437656264313731393964376131633836666136
      3631633661303764386637663037643365633663643031640a666338393031303765666232333233
      31633335343934616363653261653866626135333964643938303761306134313439633866346666
      6239613661663463610a343633363431343666383961386265313364393561326635653166623938
      63356133386236353963653235613638366333643063343939646362616233323432343033303065
      6337353733386462366531656535623232373532356532383964
    baremetal_subnet_id: subnet-0eb797bfbf9925dd6
    galaxy_openshift_ipi_role: 'arn:aws-us-gov:iam::336073300945:role/galaxy-openshift-ipi-iadm'
  openshift_fips: "false"

lmco_redhat_odf:
  operator_hub:
    add_resources: true
  default_sc: ocs-storagecluster-ceph-rbd

lmco_openshift_local_storage:
  operator_hub:
    add_resources: true
