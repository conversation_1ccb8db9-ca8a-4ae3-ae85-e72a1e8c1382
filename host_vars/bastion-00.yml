# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        Galaxy Standard Host Variables                                    #
# Version:                                                                        #
#               2023-08-23 WRC. Initial                                           #
# Create Date:  2023-08-23                                                        #
# Author:       <PERSON><PERSON><PERSON>, Wayne (US) <<EMAIL>>                #
# Description:                                                                    #
#               This is where the host based dictionaries will be placed.         #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
# ------------------------------------------------------------------------------- #

ansible_host: ***************
lmco_host:
  abstract:
    name: bastion-00
    description: This is a description
    purpose: bastion
  credentials:
    ocp_user_authorized_public_key: >
      ssh-rsa
      AAAAB3NzaC1yc2EAAAADAQABAAABgQCn4KtNuUWOWZkhCNukBBTj9dP4qrgIcfmU94MuhMTxCcS1DlhZnEG4nD7yg8fPpcIwqcwVUiOwrXxcUQ6kT0s9Ct7h7AMuXi+4XZVrTjpLYul7XwQ55bhoSXPXDc5fUnId2LhcyDxNEDeZCeYkmurZ3XmUDMM8R3oEdru2GOy3lRnrXc9PrvfCNz/5XfLPZKAHKWKkGLIyXx5Io93EaxrAo3YZmBbt5qZsU8f8Focm/VRjWfcLdNMni6SkfrPux5bAkmPUk/foCSHR1gb4bdcnifv1Ep3Fj0JFInBl4rqKpuoh45mupS6fkLltGp1ZNy76RY+wsypX+s6QzSHMlE16cQTaVjO399j45iDLRoJU2XGcRTsxiFbe5JfKNRCOyKZYYE9eNl8YiEDgv0QI2gZWhF2CYdCd8M+swDrqhlVgnKHLBkSmWVwW97TNDRzmz5j9B2YpWh3QHAjLLDeh/lzVKvyjeSgWmTKLOJPOOYnO5O9ynQyI3IoKwfAYO8yXbq8=
    ocp_user_password: !vault |
      $ANSIBLE_VAULT;1.2;AES256;galaxy
      31623062393166653832386131613265343539306536366230366161633034316266666266396336
      3730323030346231623035633232386235373666366434330a623061313665336436363562366634
      35383736393065346463666637353430346361316132383434333838646333396635653035613930
      3338363630626365620a356239346634653762336531393961303239636430306639306530373264
      3132
  network:
    public_ip: ***************
  options:
    shell_color: 0;32
  aws:
    # ami_id: ami-0583d8c7a9c35822c # rhel 9.4 pub cloud
    # ami_id: ami-0c7af5fe939f2677f # rhel 9.5 pub cloud
    ami_id: ami-0ea07adf4c65d1033 # rhel 9.5 gov cloud
