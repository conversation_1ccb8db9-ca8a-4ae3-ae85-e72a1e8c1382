# OpenShift Deployment Script Documentation

## Overview

The `openshift-build.bash` script automates the deployment of an OpenShift cluster (named Andromeda) using the Galaxy automation framework. This document explains the script's functionality, configuration files, and the relationship between key components.

## Andromeda and OpenShift Relationship

Andromeda is the name assigned to a specific OpenShift cluster deployment. OpenShift is Red Hat's enterprise Kubernetes platform for container orchestration.

From the configuration:
```yaml
lmco_ocp:
  openshift_cluster: andromeda
  # Additional configuration parameters...
```

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                     Deployment Environment                      │
│                                                                 │
│  ┌─────────────┐          ┌─────────────────────────────────┐  │
│  │             │          │                                 │  │
│  │  Local Host │          │        AWS Environment          │  │
│  │             │          │                                 │  │
│  │  ┌─────────┴─┐         │  ┌─────────────┐               │  │
│  │  │           │         │  │             │               │  │
│  │  │  Galaxy   │         │  │  Bastion    │               │  │
│  │  │ Container ├─────────┼─►│   Host      │               │  │
│  │  │           │         │  │             │               │  │
│  │  └───────────┘         │  └──────┬──────┘               │  │
│  │                        │         │                      │  │
│  └────────────────────────┘         ▼                      │  │
│                             ┌────────────────┐             │  │
│                             │                │             │  │
│                             │   OpenShift    │             │  │
│                             │   Cluster      │             │  │
│                             │  (Andromeda)   │             │  │
│                             │                │             │  │
│                             └────────────────┘             │  │
│                                                            │  │
│                                                            │  │
└────────────────────────────────────────────────────────────┘  │
                                                                 │
```

## Script Functionality

The `openshift-build.bash` script performs the following operations:

### 1. Container Image Preparation

- Loads the Galaxy container image from a tar file
- Tags the image for use in deployment

### 2. Network Configuration

- Sets the build port (default: 8080)
- Automatically detects and configures network interfaces and IP addresses
- Configures both LMI (Local Management Interface) and MGMT (Management) IP addresses

### 3. Security Configuration

- Sets appropriate file permissions and security contexts
- Configures SSH keys and vault passwords for secure automation

### 4. Ansible Collection Management

- Copies required Ansible collections for OpenShift deployment
- Ensures collections are accessible within the container environment

### 5. Container Execution

- Runs a containerized deployment using Podman with:
  - Proper volume mounts for configuration files
  - Security settings for container execution
  - Network port mapping
  - User namespace configuration

### 6. Deployment Execution

- Executes the Ansible playbook with the Galaxy builder role
- Uses the inventory defined in `inventory.yml`
- Logs all output to a timestamped file for auditing and troubleshooting

## Deployment Flow Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Load Galaxy    │     │  Configure      │     │  Setup SSH      │
│  Container      ├────►│  Network        ├────►│  & Security     │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Execute        │     │  Run Container  │     │  Prepare        │
│  Deployment     │◄────┤  with Ansible   │◄────┤  Ansible        │
│                 │     │                 │     │  Collections    │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Script Code Explanation

```bash
#!/bin/bash
# This script automates the deployment of an OpenShift cluster using Galaxy automation

# Load the Galaxy container image from tar file
output=$(podman load -i $(pwd)/lmco.galaxy.tar)
image=$(echo $output | sed -n -e 's/^Loaded image: //p')
podman tag $image galaxy

# Configure network settings
export GALAXY_BUILD_PORT=${GALAXY_BUILD_PORT:-8080}
if [ -z "${GALAXY_BUILD_LMI_IP}" ]; then
  interface=$(ip route show 0.0.0.0/0 | sort -k5n | head -n1 | awk '{print $5}')
  export GALAXY_BUILD_LMI_IP=$(ip -o -4 addr show dev "${interface}" | awk '{split($4,a,"/"); print a[1]}' | head -n1 )
fi

# Set up security and credentials
mkdir -p ~/.ssh
chmod 700 ~/.ssh
cp -f $(pwd)/files/id_rsa ~/.ssh/
chmod 600 ~/.ssh/id_rsa
export ANSIBLE_HOST_KEY_CHECKING=False

# Prepare Ansible collections
mkdir -p $(pwd)/ansible_collections
cp -rf ~/.ansible/collections/ansible_collections/* $(pwd)/ansible_collections/

# Execute container with proper mounts and settings
podman run --rm -it \
  -v $(pwd):/galaxy \
  -v $(pwd)/ansible_collections:/root/.ansible/collections/ansible_collections \
  -v ~/.ssh:/root/.ssh \
  -e ANSIBLE_HOST_KEY_CHECKING \
  -e GALAXY_BUILD_PORT \
  -e GALAXY_BUILD_LMI_IP \
  -p ${GALAXY_BUILD_PORT}:${GALAXY_BUILD_PORT} \
  --security-opt label=disable \
  galaxy ansible-playbook \
  -i inventory.yml \
  /usr/share/ansible/roles/galaxy_builder/playbook.yml \
  -e "galaxy_builder_role=openshift" | tee $(date +%Y%m%d-%H%M%S)-openshift-build.out
```

## Configuration Files

### Inventory Structure (`inventory.yml`)

The inventory defines the hosts and groups for deployment:

```yaml
bastion:
  hosts:
    bastion-00:

openshift:
  children:
    bastion:
```

This structure establishes:

- A `bastion` group containing the host `bastion-00`
- An `openshift` group that includes the `bastion` group as a child

This hierarchy allows the OpenShift deployment to use the bastion host for installation and configuration tasks.

## Inventory Relationship Diagram

```
┌───────────────────────────────────────┐
│                                       │
│            openshift group            │
│                                       │
│  ┌───────────────────────────────┐    │
│  │                               │    │
│  │         bastion group         │    │
│  │                               │    │
│  │  ┌─────────────────────────┐  │    │
│  │  │                         │  │    │
│  │  │      bastion-00 host    │  │    │
│  │  │                         │  │    │
│  │  └─────────────────────────┘  │    │
│  │                               │    │
│  └───────────────────────────────┘    │
│                                       │
└───────────────────────────────────────┘
```

### Host Variables

#### `host_vars/localhost.yml`

Contains variables specific to the local machine running the deployment:

```yaml
ansible_connection: local   # Ensures SSH is not used for localhost connections
```

#### `host_vars/bastion-00.yml`

Defines configuration for the bastion host:

```yaml
ansible_host: ***************  # IP address of the bastion host
lmco_host:
  abstract:
    name: bastion-00
    description: This is a description
    purpose: bastion
  credentials:
    ocp_user_authorized_public_key: >
      ssh-rsa AAAAB3NzaC1yc2E...  # SSH public key for OpenShift user
    ocp_user_password: !vault |   # Encrypted password using Ansible Vault
      $ANSIBLE_VAULT;1.2;AES256;galaxy
      31623062393166...
  network:
    public_ip: ***************
  options:
    shell_color: 0;32
  aws:
    ami_id: ami-0ea07adf4c65d1033  # RHEL 9.5 gov cloud AMI
```

This file contains:

- Connection information for the bastion host
- SSH keys and credentials (encrypted with Ansible Vault)
- Network configuration
- AWS-specific settings including the AMI ID to use

### Group Variables

#### `group_vars/bastion.yml`

Defines configuration for all hosts in the bastion group:

```yaml
lmco_bastion_products:
  # Install Linux on the bastion
  - { name: galaxy_hardware, target: aws, provider: linux, profile: default, state: present }
  # Configure bastion with required software
  - { name: galaxy_server, target: linux, provider: bastion, profile: aws, state: present }
```

This file defines the Galaxy products to install on the bastion host:

- `galaxy_hardware`: Installs Linux on AWS
- `galaxy_server`: Configures the bastion host with required software (haproxy, dhcpd, dns, etc.)

#### `group_vars/openshift.yml`

Contains OpenShift-specific configuration:

```yaml
lmco_openshift_products:
  # Install OpenShift
  - { name: galaxy_platform, target: aws, provider: okd, profile: default, state: present }
  # Base OpenShift configurations
  - { name: galaxy_platform, target: redhat_openshift, provider: base, profile: default, state: present }

lmco_ocp:
  ca_cert: "{{ lookup('env', 'PWD') }}/files/ca.crt"
  cluster_admins:
    - c1620
    - deckerd2
    # Additional admin users...
  cluster_cert: "{{ lookup('env', 'PWD') }}/files/andromeda.crt"
  cluster_key: "{{ lookup('env', 'PWD') }}/files/andromeda.key"
  force_redeployment: false
  openshift_cluster: andromeda
  project_domain: ssc.lmco.com
  sso_client_id: space-spaceit-ocp-el8000
  sso_issuer: https://authuat.p.external.lmco.com
  sso_name: LMCO-SSO
  sso_secret: !vault |
    $ANSIBLE_VAULT;1.2;AES256;galaxy
    61643262373364...
  sso_type: OpenID
  openshift_public_ip: ***************
  baremetal_network_cidr: **********/27
  aws:
    vpc_id: vpc-01d4a367eeec07620
    az: us-gov-west-1a
    region: us-gov-west-1
    aws_access_key_id: AKIAU4P4HTPI2ZNNFNNS
    aws_secret_access_key: !vault |
      $ANSIBLE_VAULT;1.2;AES256;galaxy
      66393336303233...
    baremetal_subnet_id: subnet-0eb797bfbf9925dd6
    galaxy_openshift_ipi_role: 'arn:aws-us-gov:iam::336073300945:role/galaxy-openshift-ipi-iadm'
  openshift_fips: "false"

lmco_redhat_odf:
  operator_hub:
    add_resources: true
  default_sc: ocs-storagecluster-ceph-rbd

lmco_openshift_local_storage:
  operator_hub:
    add_resources: true
```

This file contains:

- Galaxy products to install for OpenShift
- Cluster configuration including certificates and keys
- List of cluster administrators
- SSO configuration (with encrypted secrets)
- AWS-specific settings (region, VPC, subnet, credentials)
- Storage configuration for OpenShift Data Foundation (ODF)

## Configuration Relationship Diagram

```
┌───────────────────────────────────────────────────────────────────┐
│                                                                   │
│                     Configuration Structure                       │
│                                                                   │
│  ┌─────────────────┐      ┌─────────────────┐                     │
│  │                 │      │                 │                     │
│  │   inventory.yml │      │  host_vars/     │                     │
│  │                 │      │  localhost.yml  │                     │
│  └────────┬────────┘      └─────────────────┘                     │
│           │                                                       │
│           │               ┌─────────────────┐                     │
│           │               │                 │                     │
│           ├──────────────►│  host_vars/     │                     │
│           │               │  bastion-00.yml │                     │
│           │               │                 │                     │
│           │               └─────────────────┘                     │
│           │                                                       │
│           │               ┌─────────────────┐                     │
│           │               │                 │                     │
│           ├──────────────►│  group_vars/    │                     │
│           │               │  bastion.yml    │                     │
│           │               │                 │                     │
│           │               └─────────────────┘                     │
│           │                                                       │
│           │               ┌─────────────────┐                     │
│           │               │                 │                     │
│           └──────────────►│  group_vars/    │                     │
│                           │  openshift.yml  │                     │
│                           │                 │                     │
│                           └─────────────────┘                     │
│                                                                   │
└───────────────────────────────────────────────────────────────────┘
```

## Deployment Process

The deployment follows these steps:

1. **Preparation Phase**
   - The script loads the Galaxy container image
   - Sets up network and security configurations
   - Prepares Ansible collections

2. **Bastion Host Deployment**
   - Provisions an AWS EC2 instance using the specified AMI
   - Configures the bastion host with required software
   - Sets up networking and security

3. **OpenShift Cluster Deployment**
   - Uses the bastion host to deploy the OpenShift cluster (Andromeda)
   - Configures cluster certificates, authentication, and networking
   - Sets up storage using OpenShift Data Foundation

4. **Post-Deployment Configuration**
   - Configures cluster administrators
   - Sets up SSO integration
   - Applies security policies and configurations

## Deployment Process Diagram

```
┌─────────────────────────────────────────────────────────────────────┐
│                                                                     │
│                     Deployment Process                              │
│                                                                     │
│  ┌─────────────────┐     ┌─────────────────┐    ┌─────────────────┐ │
│  │                 │     │                 │    │                 │ │
│  │   Preparation   │     │  Bastion Host   │    │   OpenShift     │ │
│  │     Phase       ├────►│   Deployment    ├───►│   Cluster       │ │
│  │                 │     │                 │    │   Deployment    │ │
│  └─────────────────┘     └─────────────────┘    └────────┬────────┘ │
│                                                          │          │
│                                                          ▼          │
│                                                 ┌─────────────────┐ │
│                                                 │                 │ │
│                                                 │ Post-Deployment │ │
│                                                 │ Configuration   │ │
│                                                 │                 │ │
│                                                 └─────────────────┘ │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

## Security Considerations

The script handles sensitive information including:

- SSH keys
- Vault passwords
- AWS credentials (stored in encrypted format)
- Cluster certificates

All sensitive data is properly secured using:

- Ansible Vault encryption for passwords and secrets
- Appropriate file permissions (600 for SSH keys)
- Secure container execution with proper volume mounts
